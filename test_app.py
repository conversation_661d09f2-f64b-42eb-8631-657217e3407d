#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的测试启动脚本
"""

from flask import Flask, jsonify, send_from_directory
from flask_cors import CORS
import os

def create_test_app():
    """创建测试Flask应用"""
    app = Flask(__name__)
    CORS(app)
    
    # 健康检查
    @app.route('/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'message': '企业文件共享系统运行正常',
            'version': '1.0.0'
        })
    
    # 静态文件服务
    @app.route('/')
    def index():
        return send_from_directory('templates', 'user_login.html')
    
    @app.route('/admin_login.html')
    def admin_login():
        return send_from_directory('templates', 'admin_login.html')
    
    @app.route('/user_login.html')
    def user_login():
        return send_from_directory('templates', 'user_login.html')
    
    @app.route('/admin_dashboard.html')
    def admin_dashboard():
        return send_from_directory('templates', 'admin_dashboard.html')
    
    @app.route('/user_dashboard.html')
    def user_dashboard():
        return send_from_directory('templates', 'user_dashboard.html')
    
    @app.route('/admin_dashboard.js')
    def admin_dashboard_js():
        return send_from_directory('templates', 'admin_dashboard.js')
    
    @app.route('/<path:filename>')
    def serve_static(filename):
        # 检查文件是否存在于templates目录
        if os.path.exists(os.path.join('templates', filename)):
            return send_from_directory('templates', filename)
        return jsonify({'error': 'File not found'}), 404
    
    # 模拟API端点
    @app.route('/api/auth/admin/login', methods=['POST'])
    def mock_admin_login():
        return jsonify({'error': '数据库未连接，请先配置MySQL'}), 500
    
    @app.route('/api/auth/user/login', methods=['POST'])
    def mock_user_login():
        return jsonify({'error': '数据库未连接，请先配置MySQL'}), 500
    
    return app

if __name__ == '__main__':
    print("=" * 60)
    print("企业文件共享系统 - 测试模式")
    print("=" * 60)
    print("注意：这是测试模式，数据库功能未启用")
    print("请先配置MySQL数据库后使用完整版本")
    print("=" * 60)
    print("访问地址:")
    print("- 用户登录: http://localhost:5000/user_login.html")
    print("- 管理员登录: http://localhost:5000/admin_login.html")
    print("- 系统状态: http://localhost:5000/health")
    print("=" * 60)
    
    app = create_test_app()
    app.run(host='0.0.0.0', port=5000, debug=True)
