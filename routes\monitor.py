from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from datetime import datetime, timedelta
from sqlalchemy import func, desc

from models import db, User, ActivityLog, DownloadRecord, FileIndex, SharedFolder

monitor_bp = Blueprint('monitor', __name__)

def admin_required(f):
    """管理员权限装饰器"""
    def decorated_function(*args, **kwargs):
        claims = get_jwt()
        if not claims.get('is_admin', False):
            return jsonify({'error': '需要管理员权限'}), 403
        return f(*args, **kwargs)
    return decorated_function

@monitor_bp.route('/dashboard', methods=['GET'])
@jwt_required()
@admin_required
def get_dashboard_stats():
    """获取仪表板统计数据"""
    try:
        # 基本统计
        total_users = User.query.filter_by(is_admin=False).count()
        total_files = FileIndex.query.count()
        total_folders = SharedFolder.query.count()
        
        # 在线用户数（最近5分钟有活动的用户）
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        online_users = User.query.filter(User.last_login >= five_minutes_ago).count()
        
        # 今日统计
        today = datetime.utcnow().date()
        today_downloads = DownloadRecord.query.filter(
            func.date(DownloadRecord.created_at) == today
        ).count()
        
        today_download_size = db.session.query(
            func.sum(DownloadRecord.total_size)
        ).filter(
            func.date(DownloadRecord.created_at) == today
        ).scalar() or 0
        
        # 文件类型统计
        file_type_stats = db.session.query(
            FileIndex.file_extension,
            func.count(FileIndex.id).label('count')
        ).group_by(FileIndex.file_extension).all()
        
        # 最近活动
        recent_activities = ActivityLog.query.order_by(
            desc(ActivityLog.created_at)
        ).limit(10).all()
        
        return jsonify({
            'stats': {
                'total_users': total_users,
                'total_files': total_files,
                'total_folders': total_folders,
                'online_users': online_users,
                'today_downloads': today_downloads,
                'today_download_size': today_download_size
            },
            'file_types': [
                {'extension': ext, 'count': count}
                for ext, count in file_type_stats
            ],
            'recent_activities': [
                activity.to_dict() for activity in recent_activities
            ]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取统计数据失败: {str(e)}'}), 500

@monitor_bp.route('/online-users', methods=['GET'])
@jwt_required()
@admin_required
def get_online_users():
    """获取在线用户列表"""
    try:
        # 最近5分钟有活动的用户
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        online_users = User.query.filter(
            User.last_login >= five_minutes_ago,
            User.is_admin == False
        ).all()
        
        return jsonify({
            'online_users': [
                {
                    'id': user.id,
                    'username': user.username,
                    'user_group': user.user_group.name if user.user_group else None,
                    'last_login': user.last_login.isoformat() if user.last_login else None
                }
                for user in online_users
            ]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取在线用户失败: {str(e)}'}), 500

@monitor_bp.route('/activity-logs', methods=['GET'])
@jwt_required()
@admin_required
def get_activity_logs():
    """获取活动日志"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        action = request.args.get('action')
        severity = request.args.get('severity')
        user_id = request.args.get('user_id', type=int)
        
        query = ActivityLog.query
        
        if action:
            query = query.filter(ActivityLog.action == action)
        
        if severity:
            query = query.filter(ActivityLog.severity == severity)
        
        if user_id:
            query = query.filter(ActivityLog.user_id == user_id)
        
        pagination = query.order_by(desc(ActivityLog.created_at)).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'logs': [log.to_dict() for log in pagination.items],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'per_page': per_page
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取活动日志失败: {str(e)}'}), 500

@monitor_bp.route('/download-records', methods=['GET'])
@jwt_required()
@admin_required
def get_download_records():
    """获取下载记录"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        user_id = request.args.get('user_id', type=int)
        download_type = request.args.get('download_type')
        
        query = DownloadRecord.query
        
        if user_id:
            query = query.filter(DownloadRecord.user_id == user_id)
        
        if download_type:
            query = query.filter(DownloadRecord.download_type == download_type)
        
        pagination = query.order_by(desc(DownloadRecord.created_at)).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'records': [record.to_dict() for record in pagination.items],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'per_page': per_page
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取下载记录失败: {str(e)}'}), 500
