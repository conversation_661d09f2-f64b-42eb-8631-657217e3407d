const API_BASE = '/api';
let currentUser = null;

// 页面加载时初始化
window.addEventListener('load', function() {
    checkAuth();
    loadDashboard();
});

// 检查认证状态
async function checkAuth() {
    const token = localStorage.getItem('access_token');
    if (!token) {
        window.location.href = 'admin_login.html';
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/auth/verify`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.valid && data.user.is_admin) {
                currentUser = data.user;
                document.getElementById('adminName').textContent = data.user.username;
            } else {
                throw new Error('Not admin');
            }
        } else {
            throw new Error('Invalid token');
        }
    } catch (error) {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');
        window.location.href = 'admin_login.html';
    }
}

// API请求封装
async function apiRequest(url, options = {}) {
    const token = localStorage.getItem('access_token');
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    };

    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(`${API_BASE}${url}`, mergedOptions);
        
        if (response.status === 401) {
            // Token过期，尝试刷新
            const refreshed = await refreshToken();
            if (refreshed) {
                // 重新发送请求
                mergedOptions.headers['Authorization'] = `Bearer ${localStorage.getItem('access_token')}`;
                return await fetch(`${API_BASE}${url}`, mergedOptions);
            } else {
                window.location.href = 'admin_login.html';
                return;
            }
        }

        return response;
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// 刷新Token
async function refreshToken() {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) return false;

    try {
        const response = await fetch(`${API_BASE}/auth/refresh`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${refreshToken}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            localStorage.setItem('access_token', data.access_token);
            return true;
        }
    } catch (error) {
        console.error('Token refresh failed:', error);
    }

    return false;
}

// 显示页面
function showPage(pageId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });

    // 移除所有导航项的active类
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // 显示选中的页面
    document.getElementById(pageId).classList.add('active');

    // 添加导航项的active类
    event.target.classList.add('active');

    // 根据页面加载相应数据
    switch(pageId) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'users':
            loadUsers();
            break;
        case 'groups':
            loadGroups();
            break;
        case 'folders':
            loadFolders();
            break;
        case 'settings':
            loadSettings();
            break;
        case 'monitor':
            loadMonitor();
            break;
        case 'logs':
            loadLogs();
            break;
    }
}

// 加载仪表板数据
async function loadDashboard() {
    try {
        const response = await apiRequest('/monitor/dashboard');
        if (response.ok) {
            const data = await response.json();
            
            // 更新统计数据
            document.getElementById('totalUsers').textContent = data.stats.total_users;
            document.getElementById('totalFiles').textContent = data.stats.total_files;
            document.getElementById('onlineUsers').textContent = data.stats.online_users;
            document.getElementById('todayDownloads').textContent = data.stats.today_downloads;

            // 显示最近活动
            const activitiesHtml = data.recent_activities.map(activity => `
                <div style="padding: 10px; border-bottom: 1px solid #eee;">
                    <strong>${activity.username || 'System'}</strong> - ${activity.action}
                    <br>
                    <small style="color: #666;">${new Date(activity.created_at).toLocaleString()}</small>
                </div>
            `).join('');

            document.getElementById('recentActivities').innerHTML = activitiesHtml || '<p>暂无活动记录</p>';
        }
    } catch (error) {
        console.error('Failed to load dashboard:', error);
        document.getElementById('recentActivities').innerHTML = '<p>加载失败</p>';
    }
}

// 加载用户列表
async function loadUsers() {
    try {
        const response = await apiRequest('/admin/users');
        if (response.ok) {
            const data = await response.json();
            
            const tableHtml = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>用户组</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.users.map(user => `
                            <tr>
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${user.email || '-'}</td>
                                <td>${user.user_group || '-'}</td>
                                <td>${user.is_active ? '激活' : '禁用'}</td>
                                <td>${new Date(user.created_at).toLocaleDateString()}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editUser(${user.id})">编辑</button>
                                    <button class="btn btn-danger" onclick="deleteUser(${user.id})">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            document.getElementById('usersTable').innerHTML = tableHtml;
        }
    } catch (error) {
        console.error('Failed to load users:', error);
        document.getElementById('usersTable').innerHTML = '<p>加载失败</p>';
    }
}

// 加载用户组列表
async function loadGroups() {
    try {
        const response = await apiRequest('/admin/user-groups');
        if (response.ok) {
            const data = await response.json();
            
            const tableHtml = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>描述</th>
                            <th>用户数量</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.groups.map(group => `
                            <tr>
                                <td>${group.id}</td>
                                <td>${group.name}</td>
                                <td>${group.description || '-'}</td>
                                <td>${group.user_count}</td>
                                <td>${new Date(group.created_at).toLocaleDateString()}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editGroup(${group.id})">编辑</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            document.getElementById('groupsTable').innerHTML = tableHtml;
        }
    } catch (error) {
        console.error('Failed to load groups:', error);
        document.getElementById('groupsTable').innerHTML = '<p>加载失败</p>';
    }
}

// 加载共享文件夹列表
async function loadFolders() {
    try {
        const response = await apiRequest('/admin/shared-folders');
        if (response.ok) {
            const data = await response.json();
            
            const tableHtml = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>路径</th>
                            <th>文件数量</th>
                            <th>内网访问</th>
                            <th>外网访问</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.folders.map(folder => `
                            <tr>
                                <td>${folder.id}</td>
                                <td>${folder.name}</td>
                                <td>${folder.path}</td>
                                <td>${folder.file_count}</td>
                                <td>${folder.allow_internal ? '是' : '否'}</td>
                                <td>${folder.allow_external ? '是' : '否'}</td>
                                <td>${folder.is_active ? '激活' : '禁用'}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editFolder(${folder.id})">编辑</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            document.getElementById('foldersTable').innerHTML = tableHtml;
        }
    } catch (error) {
        console.error('Failed to load folders:', error);
        document.getElementById('foldersTable').innerHTML = '<p>加载失败</p>';
    }
}

// 加载系统设置
async function loadSettings() {
    document.getElementById('settingsContent').innerHTML = '<p>系统设置功能开发中...</p>';
}

// 加载监控数据
async function loadMonitor() {
    document.getElementById('monitorContent').innerHTML = '<p>监控统计功能开发中...</p>';
}

// 加载活动日志
async function loadLogs() {
    document.getElementById('logsTable').innerHTML = '<p>活动日志功能开发中...</p>';
}

// 退出登录
async function logout() {
    try {
        await apiRequest('/auth/logout', { method: 'POST' });
    } catch (error) {
        console.error('Logout failed:', error);
    } finally {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');
        window.location.href = 'admin_login.html';
    }
}

// 模态框相关函数
function showModal(title, content) {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalBody').innerHTML = content;
    document.getElementById('modal').style.display = 'block';
}

function closeModal() {
    document.getElementById('modal').style.display = 'none';
}

// 显示创建用户模态框
function showCreateUserModal() {
    const content = `
        <form id="createUserForm">
            <div class="form-group">
                <label>用户名</label>
                <input type="text" name="username" required>
            </div>
            <div class="form-group">
                <label>密码</label>
                <input type="password" name="password" required>
            </div>
            <div class="form-group">
                <label>邮箱</label>
                <input type="email" name="email">
            </div>
            <div class="form-group">
                <label>是否为管理员</label>
                <select name="is_admin">
                    <option value="false">否</option>
                    <option value="true">是</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">创建用户</button>
        </form>
    `;
    
    showModal('创建用户', content);
    
    document.getElementById('createUserForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const userData = {
            username: formData.get('username'),
            password: formData.get('password'),
            email: formData.get('email'),
            is_admin: formData.get('is_admin') === 'true'
        };
        
        try {
            const response = await apiRequest('/admin/users', {
                method: 'POST',
                body: JSON.stringify(userData)
            });
            
            if (response.ok) {
                closeModal();
                loadUsers();
                showAlert('用户创建成功', 'success');
            } else {
                const error = await response.json();
                showAlert(error.error || '创建失败', 'error');
            }
        } catch (error) {
            showAlert('网络错误', 'error');
        }
    });
}

// 显示提示信息
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    document.body.insertBefore(alertDiv, document.body.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}
