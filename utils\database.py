from models import db, User, UserGroup, SystemSettings
import json

def init_database():
    """初始化数据库"""
    try:
        # 创建所有表
        db.create_all()
        
        # 创建默认管理员用户
        create_default_admin()
        
        # 创建默认用户组
        create_default_groups()
        
        # 初始化系统设置
        init_system_settings()
        
        print("数据库初始化完成")
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        db.session.rollback()

def create_default_admin():
    """创建默认管理员用户"""
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            is_admin=True,
            is_active=True
        )
        admin.set_password('admin123')  # 默认密码，生产环境需要修改
        db.session.add(admin)
        db.session.commit()
        print("默认管理员用户已创建: admin/admin123")

def create_default_groups():
    """创建默认用户组"""
    # 管理员组
    admin_group = UserGroup.query.filter_by(name='administrators').first()
    if not admin_group:
        admin_permissions = {
            'can_read': True,
            'can_write': True,
            'can_delete': True,
            'can_download': True,
            'can_upload': True,
            'can_manage_users': True,
            'can_manage_folders': True,
            'can_view_logs': True,
            'can_manage_settings': True,
            'max_download_size': -1,  # 无限制
            'max_downloads_per_day': -1,  # 无限制
            'can_access_external': True
        }
        admin_group = UserGroup(
            name='administrators',
            description='系统管理员组',
            permissions=json.dumps(admin_permissions)
        )
        db.session.add(admin_group)
    
    # 普通用户组
    user_group = UserGroup.query.filter_by(name='users').first()
    if not user_group:
        user_permissions = {
            'can_read': True,
            'can_write': False,
            'can_delete': False,
            'can_download': True,
            'can_upload': False,
            'can_manage_users': False,
            'can_manage_folders': False,
            'can_view_logs': False,
            'can_manage_settings': False,
            'max_download_size': 1024 * 1024 * 100,  # 100MB
            'max_downloads_per_day': 50,
            'can_access_external': False
        }
        user_group = UserGroup(
            name='users',
            description='普通用户组',
            permissions=json.dumps(user_permissions)
        )
        db.session.add(user_group)
    
    # 只读用户组
    readonly_group = UserGroup.query.filter_by(name='readonly').first()
    if not readonly_group:
        readonly_permissions = {
            'can_read': True,
            'can_write': False,
            'can_delete': False,
            'can_download': False,
            'can_upload': False,
            'can_manage_users': False,
            'can_manage_folders': False,
            'can_view_logs': False,
            'can_manage_settings': False,
            'max_download_size': 0,
            'max_downloads_per_day': 0,
            'can_access_external': False
        }
        readonly_group = UserGroup(
            name='readonly',
            description='只读用户组',
            permissions=json.dumps(readonly_permissions)
        )
        db.session.add(readonly_group)
    
    db.session.commit()
    print("默认用户组已创建")

def init_system_settings():
    """初始化系统设置"""
    default_settings = [
        # 基本设置
        ('system_name', '企业文件共享系统', '系统名称', 'basic', 'string'),
        ('system_version', '1.0.0', '系统版本', 'basic', 'string'),
        ('max_concurrent_users', '100', '最大并发用户数', 'basic', 'int'),
        ('enable_registration', 'false', '允许用户注册', 'basic', 'bool'),
        
        # 文件设置
        ('max_file_size', '1073741824', '最大文件大小(字节)', 'file', 'int'),  # 1GB
        ('allowed_extensions', '["jpg","jpeg","png","gif","bmp","tiff","tif","webp","psd","ai","eps","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","zip","rar","7z"]', '允许的文件扩展名', 'file', 'json'),
        ('enable_thumbnails', 'true', '启用缩略图', 'file', 'bool'),
        ('thumbnail_quality', '85', '缩略图质量', 'file', 'int'),
        
        # 下载设置
        ('max_download_size', '1073741824', '最大下载大小(字节)', 'download', 'int'),  # 1GB
        ('max_batch_downloads', '100', '最大批量下载数量', 'download', 'int'),
        ('encryption_threshold', '5', '加密阈值(下载次数)', 'download', 'int'),
        ('enable_download_encryption', 'true', '启用下载加密', 'download', 'bool'),
        
        # 搜索设置
        ('enable_file_search', 'true', '启用文件搜索', 'search', 'bool'),
        ('enable_image_search', 'true', '启用图像搜索', 'search', 'bool'),
        ('search_results_limit', '100', '搜索结果限制', 'search', 'int'),
        
        # 网络设置
        ('enable_external_access', 'false', '启用外网访问', 'network', 'bool'),
        ('allowed_external_ips', '[]', '允许的外网IP', 'network', 'json'),
        ('rate_limit_per_minute', '60', '每分钟请求限制', 'network', 'int'),
        
        # 安全设置
        ('max_login_attempts', '5', '最大登录尝试次数', 'security', 'int'),
        ('lockout_duration', '1800', '锁定时长(秒)', 'security', 'int'),  # 30分钟
        ('session_timeout', '86400', '会话超时(秒)', 'security', 'int'),  # 24小时
        ('enable_activity_logging', 'true', '启用活动日志', 'security', 'bool'),
        
        # 通知设置
        ('enable_notifications', 'true', '启用通知', 'notification', 'bool'),
        ('notification_message', '欢迎使用企业文件共享系统', '通知消息', 'notification', 'string'),
        ('enable_scrolling_text', 'true', '启用滚动字幕', 'notification', 'bool'),
        ('scrolling_text', '请注意文件安全，合理使用系统资源', '滚动字幕内容', 'notification', 'string'),
    ]
    
    for key, value, description, category, data_type in default_settings:
        setting = SystemSettings.query.filter_by(key=key).first()
        if not setting:
            setting = SystemSettings(
                key=key,
                value=value,
                description=description,
                category=category,
                data_type=data_type
            )
            db.session.add(setting)
    
    db.session.commit()
    print("系统设置已初始化")
