from flask import Flask, request, jsonify, send_from_directory, render_template_string
from flask_cors import CORS
from flask_jwt_extended import JWTManager
import os
import logging
from datetime import datetime

from config import config
from models import db, User, UserGroup, SystemSettings
from utils.logger import setup_logger
from utils.database import init_database

def create_app(config_name=None):
    """创建Flask应用"""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    CORS(app)
    jwt = JWTManager(app)
    
    # 设置日志
    setup_logger(app)
    
    # 注册蓝图
    from routes.admin import admin_bp
    from routes.auth import auth_bp
    from routes.files import files_bp
    from routes.search import search_bp
    from routes.download import download_bp
    from routes.upload import upload_bp
    from routes.monitor import monitor_bp
    
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(files_bp, url_prefix='/api/files')
    app.register_blueprint(search_bp, url_prefix='/api/search')
    app.register_blueprint(download_bp, url_prefix='/api/download')
    app.register_blueprint(upload_bp, url_prefix='/api/upload')
    app.register_blueprint(monitor_bp, url_prefix='/api/monitor')
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500
    
    # JWT错误处理
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({'error': 'Token has expired'}), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({'error': 'Invalid token'}), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({'error': 'Authorization token is required'}), 401
    
    # 健康检查
    @app.route('/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        })

    # 静态文件服务
    @app.route('/')
    def index():
        return send_from_directory('templates', 'user_login.html')

    @app.route('/<path:filename>')
    def serve_static(filename):
        # 检查文件是否存在于templates目录
        if os.path.exists(os.path.join('templates', filename)):
            return send_from_directory('templates', filename)
        return jsonify({'error': 'File not found'}), 404

    # 初始化数据库
    with app.app_context():
        init_database()

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(host='0.0.0.0', port=5000, debug=True)
