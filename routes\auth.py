from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity, get_jwt
from datetime import datetime, timedelta
import ipaddress

from models import db, User, ActivityLog
from utils.logger import log_activity

auth_bp = Blueprint('auth', __name__)

def get_client_ip():
    """获取客户端IP地址"""
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

def is_account_locked(user):
    """检查账户是否被锁定"""
    if user.locked_until and user.locked_until > datetime.utcnow():
        return True
    return False

def lock_account(user, duration_minutes=30):
    """锁定账户"""
    user.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
    user.login_attempts = 0
    db.session.commit()

@auth_bp.route('/admin/login', methods=['POST'])
def admin_login():
    """管理员登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400
        
        # 查找用户
        user = User.query.filter_by(username=username).first()
        
        if not user or not user.is_admin:
            log_activity(
                user_id=None,
                action='admin_login_failed',
                resource_type='auth',
                details=f'管理员登录失败: 用户不存在或非管理员 - {username}',
                ip_address=get_client_ip(),
                severity='warning'
            )
            return jsonify({'error': '用户名或密码错误'}), 401
        
        # 检查账户是否被锁定
        if is_account_locked(user):
            remaining_time = (user.locked_until - datetime.utcnow()).total_seconds()
            return jsonify({
                'error': '账户已被锁定',
                'remaining_time': int(remaining_time)
            }), 423
        
        # 检查账户是否激活
        if not user.is_active:
            return jsonify({'error': '账户已被禁用'}), 403
        
        # 验证密码
        if not user.check_password(password):
            user.login_attempts += 1
            
            # 检查是否需要锁定账户
            if user.login_attempts >= 5:  # 可以从系统设置中读取
                lock_account(user)
                log_activity(
                    user_id=user.id,
                    action='account_locked',
                    resource_type='auth',
                    details=f'账户因多次登录失败被锁定',
                    ip_address=get_client_ip(),
                    severity='warning'
                )
                return jsonify({'error': '登录失败次数过多，账户已被锁定30分钟'}), 423
            
            db.session.commit()
            
            log_activity(
                user_id=user.id,
                action='admin_login_failed',
                resource_type='auth',
                details=f'管理员登录失败: 密码错误',
                ip_address=get_client_ip(),
                severity='warning'
            )
            
            return jsonify({
                'error': '用户名或密码错误',
                'remaining_attempts': 5 - user.login_attempts
            }), 401
        
        # 登录成功
        user.login_attempts = 0
        user.last_login = datetime.utcnow()
        user.locked_until = None
        db.session.commit()
        
        # 创建JWT令牌
        access_token = create_access_token(
            identity=user.id,
            additional_claims={'is_admin': True, 'username': user.username}
        )
        refresh_token = create_refresh_token(identity=user.id)
        
        # 记录登录日志
        log_activity(
            user_id=user.id,
            action='admin_login_success',
            resource_type='auth',
            details=f'管理员登录成功',
            ip_address=get_client_ip(),
            severity='info'
        )
        
        return jsonify({
            'message': '登录成功',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_admin': user.is_admin,
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'登录失败: {str(e)}'}), 500

@auth_bp.route('/user/login', methods=['POST'])
def user_login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400
        
        # 查找用户
        user = User.query.filter_by(username=username).first()
        
        if not user or user.is_admin:
            log_activity(
                user_id=None,
                action='user_login_failed',
                resource_type='auth',
                details=f'用户登录失败: 用户不存在或为管理员 - {username}',
                ip_address=get_client_ip(),
                severity='warning'
            )
            return jsonify({'error': '用户名或密码错误'}), 401
        
        # 检查账户是否被锁定
        if is_account_locked(user):
            remaining_time = (user.locked_until - datetime.utcnow()).total_seconds()
            return jsonify({
                'error': '账户已被锁定',
                'remaining_time': int(remaining_time)
            }), 423
        
        # 检查账户是否激活
        if not user.is_active:
            return jsonify({'error': '账户已被禁用'}), 403
        
        # 验证密码
        if not user.check_password(password):
            user.login_attempts += 1
            
            # 检查是否需要锁定账户
            if user.login_attempts >= 5:
                lock_account(user)
                log_activity(
                    user_id=user.id,
                    action='account_locked',
                    resource_type='auth',
                    details=f'账户因多次登录失败被锁定',
                    ip_address=get_client_ip(),
                    severity='warning'
                )
                return jsonify({'error': '登录失败次数过多，账户已被锁定30分钟'}), 423
            
            db.session.commit()
            
            log_activity(
                user_id=user.id,
                action='user_login_failed',
                resource_type='auth',
                details=f'用户登录失败: 密码错误',
                ip_address=get_client_ip(),
                severity='warning'
            )
            
            return jsonify({
                'error': '用户名或密码错误',
                'remaining_attempts': 5 - user.login_attempts
            }), 401
        
        # 登录成功
        user.login_attempts = 0
        user.last_login = datetime.utcnow()
        user.locked_until = None
        db.session.commit()
        
        # 创建JWT令牌
        access_token = create_access_token(
            identity=user.id,
            additional_claims={'is_admin': False, 'username': user.username}
        )
        refresh_token = create_refresh_token(identity=user.id)
        
        # 记录登录日志
        log_activity(
            user_id=user.id,
            action='user_login_success',
            resource_type='auth',
            details=f'用户登录成功',
            ip_address=get_client_ip(),
            severity='info'
        )
        
        return jsonify({
            'message': '登录成功',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'user_group': user.user_group.name if user.user_group else None,
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'登录失败: {str(e)}'}), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    try:
        user_id = get_jwt_identity()
        claims = get_jwt()
        
        # 记录登出日志
        log_activity(
            user_id=user_id,
            action='logout',
            resource_type='auth',
            details=f'用户登出',
            ip_address=get_client_ip(),
            severity='info'
        )
        
        return jsonify({'message': '登出成功'}), 200
        
    except Exception as e:
        return jsonify({'error': f'登出失败: {str(e)}'}), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新访问令牌"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user or not user.is_active:
            return jsonify({'error': '用户不存在或已被禁用'}), 401
        
        # 创建新的访问令牌
        access_token = create_access_token(
            identity=user.id,
            additional_claims={'is_admin': user.is_admin, 'username': user.username}
        )
        
        return jsonify({'access_token': access_token}), 200
        
    except Exception as e:
        return jsonify({'error': f'刷新令牌失败: {str(e)}'}), 500

@auth_bp.route('/verify', methods=['GET'])
@jwt_required()
def verify_token():
    """验证令牌"""
    try:
        user_id = get_jwt_identity()
        claims = get_jwt()
        user = User.query.get(user_id)
        
        if not user or not user.is_active:
            return jsonify({'error': '用户不存在或已被禁用'}), 401
        
        return jsonify({
            'valid': True,
            'user': {
                'id': user.id,
                'username': user.username,
                'is_admin': user.is_admin,
                'user_group': user.user_group.name if user.user_group else None
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'验证失败: {str(e)}'}), 500
