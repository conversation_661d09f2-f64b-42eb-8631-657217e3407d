# 企业文件共享系统

一个功能强大的企业级文件共享系统，支持多用户权限管理、文件搜索、安全下载等功能。

## 系统特性

### 核心功能
- ✅ **用户管理**: 多级用户权限控制，支持用户组管理
- ✅ **文件管理**: 共享文件夹管理，支持权限配置
- ✅ **安全认证**: JWT令牌认证，防暴力破解
- ✅ **活动监控**: 完整的用户行为日志记录
- ✅ **管理后台**: 功能完整的管理员控制台
- 🚧 **双搜索引擎**: Everything-like文件搜索 + 图像识别搜索
- 🚧 **缩略图支持**: 多种图片格式缩略图生成
- 🚧 **加密下载**: 可配置的下载加密功能
- 🚧 **实时监控**: 系统状态和用户活动监控

### 技术特点
- **前后端分离**: 管理员和用户界面完全独立
- **现代化UI**: 响应式设计，美观易用
- **安全可靠**: 多层安全防护，数据加密传输
- **高性能**: 优化的数据库设计和缓存机制
- **易部署**: 无需Docker，直接在Windows上部署

## 系统要求

### 软件环境
- Python 3.7+
- MySQL 5.7+
- Windows 10/11 或 Windows Server

### Python依赖
```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
PyMySQL==1.1.0
Pillow==10.0.1
opencv-python==4.8.1.78
python-magic==0.4.27
cryptography==41.0.7
bcrypt==4.0.1
python-dotenv==1.0.0
watchdog==3.0.0
psutil==5.9.6
requests==2.31.0
```

## 快速开始

### 1. 环境准备

1. **安装Python 3.7+**
   - 从 [python.org](https://www.python.org/) 下载并安装
   - 确保添加到系统PATH

2. **安装MySQL**
   - 下载并安装MySQL 5.7+
   - 创建数据库用户（默认: root/123456）
   - 启动MySQL服务

### 2. 系统安装

1. **下载源码**
   ```bash
   # 解压到目标目录，例如 C:\FileShareSystem
   ```

2. **安装依赖**
   ```bash
   cd C:\FileShareSystem
   pip install -r requirements.txt
   ```

3. **配置数据库**
   - 编辑 `config.py` 文件
   - 修改数据库连接信息（如需要）

### 3. 启动系统

```bash
python run.py
```

系统启动后会自动：
- 检查依赖环境
- 创建数据库和表结构
- 初始化默认管理员账户
- 启动Web服务

### 4. 访问系统

- **管理员登录**: http://localhost:5000/admin_login.html
- **用户登录**: http://localhost:5000/user_login.html
- **系统健康检查**: http://localhost:5000/health

### 5. 默认账户

- **管理员账户**: admin / admin123
- **首次登录后请立即修改密码**

## 系统配置

### 数据库配置
```python
# config.py
MYSQL_HOST = 'localhost'
MYSQL_PORT = 3306
MYSQL_USER = 'root'
MYSQL_PASSWORD = '123456'
MYSQL_DATABASE = 'file_sharing_system'
```

### 文件存储配置
```python
# 缩略图存储目录
THUMBNAIL_FOLDER = './thumbnails'

# 临时文件目录
TEMP_FOLDER = './temp'

# 支持的图片格式
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.psd', '.ai', '.eps']
```

### 安全配置
```python
# JWT密钥（生产环境请修改）
JWT_SECRET_KEY = 'your-secret-key-change-in-production'

# 最大登录尝试次数
MAX_LOGIN_ATTEMPTS = 5

# 账户锁定时长（秒）
LOCKOUT_DURATION = 1800  # 30分钟
```

## 功能说明

### 管理员功能
1. **用户管理**
   - 创建、编辑、删除用户
   - 用户组权限配置
   - 账户状态管理

2. **文件夹管理**
   - 添加共享文件夹
   - 权限配置
   - 内外网访问控制

3. **系统监控**
   - 实时统计数据
   - 用户活动日志
   - 下载记录查看

4. **系统设置**
   - 基本参数配置
   - 安全策略设置
   - 功能开关控制

### 用户功能
1. **文件浏览** (开发中)
   - 文件夹树形结构
   - 文件预览
   - 缩略图显示

2. **文件搜索** (开发中)
   - 文件名搜索
   - 图像识别搜索
   - 高级筛选

3. **文件下载** (开发中)
   - 单文件下载
   - 批量下载
   - 加密下载

4. **个人中心** (开发中)
   - 下载历史
   - 使用统计
   - 个人设置

## 开发计划

### 第一阶段 ✅ (已完成)
- [x] 基础架构搭建
- [x] 用户认证系统
- [x] 管理员后台
- [x] 数据库设计
- [x] 基础API接口

### 第二阶段 🚧 (开发中)
- [ ] 文件索引引擎
- [ ] 文件搜索功能
- [ ] 缩略图生成
- [ ] 文件下载功能
- [ ] 用户界面完善

### 第三阶段 📋 (计划中)
- [ ] 图像识别搜索
- [ ] 加密下载系统
- [ ] 实时监控面板
- [ ] 移动端适配

### 第四阶段 📋 (计划中)
- [ ] 远程管理功能
- [ ] 高级权限控制
- [ ] 性能优化
- [ ] 安全加固

## 技术架构

```
├── 前端界面
│   ├── 管理员界面 (HTML/CSS/JS)
│   └── 用户界面 (HTML/CSS/JS)
├── 后端API
│   ├── 认证模块 (Flask-JWT-Extended)
│   ├── 用户管理 (SQLAlchemy)
│   ├── 文件管理 (文件系统)
│   └── 监控日志 (数据库)
├── 数据存储
│   ├── MySQL数据库 (用户、权限、日志)
│   ├── 文件系统 (共享文件)
│   └── 缓存系统 (缩略图、索引)
└── 搜索引擎
    ├── 文件名搜索 (自建索引)
    └── 图像搜索 (OpenCV)
```

## 常见问题

### Q: 如何修改默认端口？
A: 编辑 `run.py` 文件中的 `app.run(port=5000)` 参数

### Q: 如何添加共享文件夹？
A: 登录管理员后台，在"共享文件夹"页面添加

### Q: 忘记管理员密码怎么办？
A: 删除数据库重新初始化，或直接修改数据库中的密码哈希

### Q: 如何备份数据？
A: 备份MySQL数据库和配置的共享文件夹

## 技术支持

如有问题或建议，请联系开发团队。

## 许可证

本项目为企业内部使用，版权所有。
