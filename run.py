#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业文件共享系统启动脚本
"""

import os
import sys
import subprocess
from app import create_app

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_jwt_extended
        import pymysql
        import PIL
        import cv2
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_mysql_connection():
    """检查MySQL连接"""
    try:
        import pymysql
        from config import Config
        
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            charset='utf8mb4'
        )
        
        # 创建数据库（如果不存在）
        with connection.cursor() as cursor:
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {Config.MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        
        connection.commit()
        connection.close()
        print("✓ MySQL连接正常")
        return True
    except Exception as e:
        print(f"✗ MySQL连接失败: {e}")
        print("请检查MySQL服务是否启动，以及配置信息是否正确")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("✗ 依赖安装失败")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("企业文件共享系统")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("✗ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查依赖
    if not check_dependencies():
        choice = input("是否自动安装依赖? (y/n): ")
        if choice.lower() == 'y':
            if not install_dependencies():
                sys.exit(1)
        else:
            sys.exit(1)
    
    # 检查MySQL连接
    if not check_mysql_connection():
        sys.exit(1)
    
    # 创建Flask应用
    print("正在启动应用...")
    app = create_app()
    
    print("=" * 50)
    print("系统启动成功!")
    print("管理员登录: http://localhost:5000/admin_login.html")
    print("用户登录: http://localhost:5000/user_login.html")
    print("API文档: http://localhost:5000/health")
    print("默认管理员账户: admin/admin123")
    print("=" * 50)
    
    # 启动应用
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n系统已停止")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == '__main__':
    main()
