<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 企业文件共享系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 420px;
            text-align: center;
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .logo p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 14px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #74b9ff;
            box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(116, 185, 255, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            border-left: 4px solid #c33;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            border-left: 4px solid #3c3;
        }

        .loading {
            display: none;
            margin-top: 10px;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #74b9ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            margin-top: 30px;
            color: #666;
            font-size: 12px;
        }

        .features {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: left;
        }

        .features h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 5px 0;
            color: #666;
            font-size: 14px;
        }

        .features li::before {
            content: '✓';
            color: #74b9ff;
            font-weight: bold;
            margin-right: 8px;
        }

        .notification {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>文件共享系统</h1>
            <p>安全 · 高效 · 便捷</p>
        </div>

        <div class="notification">
            <strong>系统通知：</strong> 欢迎使用企业文件共享系统，请妥善保管您的账户信息
        </div>

        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required placeholder="请输入用户名">
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码">
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                登录系统
            </button>

            <div class="loading" id="loading"></div>
        </form>

        <div class="features">
            <h3>系统特色</h3>
            <ul>
                <li>支持多种文件格式预览</li>
                <li>智能文件搜索功能</li>
                <li>安全的文件下载</li>
                <li>实时在线协作</li>
                <li>完善的权限管理</li>
            </ul>
        </div>

        <div class="footer">
            <p>&copy; 2024 企业文件共享系统. 版权所有.</p>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            // 隐藏之前的消息
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            
            // 显示加载状态
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            loading.style.display = 'block';
            
            try {
                const response = await fetch(`${API_BASE}/auth/user/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 登录成功
                    localStorage.setItem('access_token', data.access_token);
                    localStorage.setItem('refresh_token', data.refresh_token);
                    localStorage.setItem('user_info', JSON.stringify(data.user));
                    
                    successMessage.textContent = '登录成功，正在跳转...';
                    successMessage.style.display = 'block';
                    
                    // 跳转到用户主页
                    setTimeout(() => {
                        window.location.href = 'user_dashboard.html';
                    }, 1000);
                } else {
                    // 登录失败
                    let errorText = data.error || '登录失败';
                    if (data.remaining_attempts !== undefined) {
                        errorText += ` (剩余尝试次数: ${data.remaining_attempts})`;
                    }
                    if (data.remaining_time !== undefined) {
                        const minutes = Math.ceil(data.remaining_time / 60);
                        errorText += ` (账户锁定，${minutes}分钟后解锁)`;
                    }
                    
                    errorMessage.textContent = errorText;
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = '网络错误，请检查网络连接后重试';
                errorMessage.style.display = 'block';
            } finally {
                // 恢复按钮状态
                loginBtn.disabled = false;
                loginBtn.textContent = '登录系统';
                loading.style.display = 'none';
            }
        });
        
        // 检查是否已经登录
        window.addEventListener('load', function() {
            const token = localStorage.getItem('access_token');
            if (token) {
                // 验证token是否有效
                fetch(`${API_BASE}/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.valid && !data.user.is_admin) {
                        window.location.href = 'user_dashboard.html';
                    }
                })
                .catch(() => {
                    // Token无效，清除本地存储
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('refresh_token');
                    localStorage.removeItem('user_info');
                });
            }
        });
    </script>
</body>
</html>
