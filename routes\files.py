from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

files_bp = Blueprint('files', __name__)

@files_bp.route('/list', methods=['GET'])
@jwt_required()
def list_files():
    """获取文件列表"""
    return jsonify({'message': '文件列表功能待实现'}), 200

@files_bp.route('/info/<int:file_id>', methods=['GET'])
@jwt_required()
def get_file_info(file_id):
    """获取文件信息"""
    return jsonify({'message': '文件信息功能待实现'}), 200
