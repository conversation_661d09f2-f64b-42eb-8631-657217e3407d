from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from datetime import datetime
import json

from models import db, User, UserGroup, SharedFolder, SystemSettings, ActivityLog, DownloadRecord
from utils.logger import log_activity

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    """管理员权限装饰器"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        claims = get_jwt()
        if not claims.get('is_admin', False):
            return jsonify({'error': '需要管理员权限'}), 403
        return f(*args, **kwargs)
    return decorated_function

def get_client_ip():
    """获取客户端IP地址"""
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

# 用户管理
@admin_bp.route('/users', methods=['GET'])
@jwt_required()
@admin_required
def get_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        
        query = User.query
        
        if search:
            query = query.filter(
                db.or_(
                    User.username.contains(search),
                    User.email.contains(search)
                )
            )
        
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        users = [user.to_dict() for user in pagination.items]
        
        return jsonify({
            'users': users,
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'per_page': per_page
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取用户列表失败: {str(e)}'}), 500

@admin_bp.route('/users', methods=['POST'])
@jwt_required()
@admin_required
def create_user():
    """创建用户"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        email = data.get('email')
        user_group_id = data.get('user_group_id')
        is_admin = data.get('is_admin', False)
        
        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return jsonify({'error': '用户名已存在'}), 400
        
        # 检查邮箱是否已存在
        if email and User.query.filter_by(email=email).first():
            return jsonify({'error': '邮箱已存在'}), 400
        
        # 创建用户
        user = User(
            username=username,
            email=email,
            user_group_id=user_group_id,
            is_admin=is_admin
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        # 记录日志
        admin_id = get_jwt_identity()
        log_activity(
            user_id=admin_id,
            action='create_user',
            resource_type='user',
            resource_id=user.id,
            details=f'创建用户: {username}',
            ip_address=get_client_ip()
        )
        
        return jsonify({
            'message': '用户创建成功',
            'user': user.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建用户失败: {str(e)}'}), 500

@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_user(user_id):
    """更新用户"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()
        
        # 更新用户信息
        if 'email' in data:
            # 检查邮箱是否已被其他用户使用
            if data['email'] and User.query.filter(
                User.email == data['email'], User.id != user_id
            ).first():
                return jsonify({'error': '邮箱已被其他用户使用'}), 400
            user.email = data['email']
        
        if 'user_group_id' in data:
            user.user_group_id = data['user_group_id']
        
        if 'is_admin' in data:
            user.is_admin = data['is_admin']
        
        if 'is_active' in data:
            user.is_active = data['is_active']
        
        if 'password' in data and data['password']:
            user.set_password(data['password'])
        
        db.session.commit()
        
        # 记录日志
        admin_id = get_jwt_identity()
        log_activity(
            user_id=admin_id,
            action='update_user',
            resource_type='user',
            resource_id=user.id,
            details=f'更新用户: {user.username}',
            ip_address=get_client_ip()
        )
        
        return jsonify({
            'message': '用户更新成功',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新用户失败: {str(e)}'}), 500

@admin_bp.route('/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_user(user_id):
    """删除用户"""
    try:
        user = User.query.get_or_404(user_id)
        username = user.username
        
        # 不能删除自己
        admin_id = get_jwt_identity()
        if user_id == admin_id:
            return jsonify({'error': '不能删除自己的账户'}), 400
        
        db.session.delete(user)
        db.session.commit()
        
        # 记录日志
        log_activity(
            user_id=admin_id,
            action='delete_user',
            resource_type='user',
            resource_id=user_id,
            details=f'删除用户: {username}',
            ip_address=get_client_ip(),
            severity='warning'
        )
        
        return jsonify({'message': '用户删除成功'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除用户失败: {str(e)}'}), 500

# 用户组管理
@admin_bp.route('/user-groups', methods=['GET'])
@jwt_required()
@admin_required
def get_user_groups():
    """获取用户组列表"""
    try:
        groups = UserGroup.query.all()
        return jsonify({
            'groups': [group.to_dict() for group in groups]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取用户组列表失败: {str(e)}'}), 500

@admin_bp.route('/user-groups', methods=['POST'])
@jwt_required()
@admin_required
def create_user_group():
    """创建用户组"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')
        permissions = data.get('permissions', {})
        
        if not name:
            return jsonify({'error': '用户组名称不能为空'}), 400
        
        # 检查名称是否已存在
        if UserGroup.query.filter_by(name=name).first():
            return jsonify({'error': '用户组名称已存在'}), 400
        
        # 创建用户组
        group = UserGroup(
            name=name,
            description=description,
            permissions=json.dumps(permissions)
        )
        
        db.session.add(group)
        db.session.commit()
        
        # 记录日志
        admin_id = get_jwt_identity()
        log_activity(
            user_id=admin_id,
            action='create_user_group',
            resource_type='user_group',
            resource_id=group.id,
            details=f'创建用户组: {name}',
            ip_address=get_client_ip()
        )
        
        return jsonify({
            'message': '用户组创建成功',
            'group': group.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建用户组失败: {str(e)}'}), 500

# 共享文件夹管理
@admin_bp.route('/shared-folders', methods=['GET'])
@jwt_required()
@admin_required
def get_shared_folders():
    """获取共享文件夹列表"""
    try:
        folders = SharedFolder.query.all()
        return jsonify({
            'folders': [folder.to_dict() for folder in folders]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取共享文件夹列表失败: {str(e)}'}), 500

@admin_bp.route('/shared-folders', methods=['POST'])
@jwt_required()
@admin_required
def create_shared_folder():
    """创建共享文件夹"""
    try:
        data = request.get_json()
        name = data.get('name')
        path = data.get('path')
        description = data.get('description', '')
        permissions = data.get('permissions', {})
        allow_internal = data.get('allow_internal', True)
        allow_external = data.get('allow_external', False)
        
        if not name or not path:
            return jsonify({'error': '名称和路径不能为空'}), 400
        
        # 检查路径是否已存在
        if SharedFolder.query.filter_by(path=path).first():
            return jsonify({'error': '路径已存在'}), 400
        
        # 创建共享文件夹
        folder = SharedFolder(
            name=name,
            path=path,
            description=description,
            permissions=json.dumps(permissions),
            allow_internal=allow_internal,
            allow_external=allow_external
        )
        
        db.session.add(folder)
        db.session.commit()
        
        # 记录日志
        admin_id = get_jwt_identity()
        log_activity(
            user_id=admin_id,
            action='create_shared_folder',
            resource_type='shared_folder',
            resource_id=folder.id,
            details=f'创建共享文件夹: {name} ({path})',
            ip_address=get_client_ip()
        )
        
        return jsonify({
            'message': '共享文件夹创建成功',
            'folder': folder.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建共享文件夹失败: {str(e)}'}), 500

# 系统设置管理
@admin_bp.route('/settings', methods=['GET'])
@jwt_required()
@admin_required
def get_settings():
    """获取系统设置"""
    try:
        category = request.args.get('category')

        query = SystemSettings.query
        if category:
            query = query.filter_by(category=category)

        settings = query.all()

        # 按类别分组
        settings_by_category = {}
        for setting in settings:
            if setting.category not in settings_by_category:
                settings_by_category[setting.category] = []
            settings_by_category[setting.category].append(setting.to_dict())

        return jsonify({'settings': settings_by_category}), 200

    except Exception as e:
        return jsonify({'error': f'获取系统设置失败: {str(e)}'}), 500

@admin_bp.route('/settings/<setting_key>', methods=['PUT'])
@jwt_required()
@admin_required
def update_setting(setting_key):
    """更新系统设置"""
    try:
        setting = SystemSettings.query.filter_by(key=setting_key).first_or_404()
        data = request.get_json()
        value = data.get('value')

        if value is None:
            return jsonify({'error': '设置值不能为空'}), 400

        setting.set_value(value)
        db.session.commit()

        # 记录日志
        admin_id = get_jwt_identity()
        log_activity(
            user_id=admin_id,
            action='update_setting',
            resource_type='system_setting',
            resource_id=setting.id,
            details=f'更新系统设置: {setting_key} = {value}',
            ip_address=get_client_ip()
        )

        return jsonify({
            'message': '设置更新成功',
            'setting': setting.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新设置失败: {str(e)}'}), 500
