<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员控制台 - 企业文件共享系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            display: flex;
            min-height: calc(100vh - 80px);
        }

        .sidebar {
            width: 250px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 2rem 0;
        }

        .nav-item {
            padding: 1rem 2rem;
            cursor: pointer;
            transition: background 0.3s;
            border-left: 3px solid transparent;
        }

        .nav-item:hover {
            background: #f8f9fa;
        }

        .nav-item.active {
            background: #e3f2fd;
            border-left-color: #667eea;
            color: #667eea;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            color: #666;
            font-size: 14px;
        }

        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            color: #333;
            font-size: 18px;
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .close {
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .alert {
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 2rem;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>企业文件共享系统 - 管理员控制台</h1>
        <div class="user-info">
            <span id="adminName">管理员</span>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>
    </div>

    <div class="container">
        <div class="sidebar">
            <div class="nav-item active" onclick="showPage('dashboard')">
                📊 仪表板
            </div>
            <div class="nav-item" onclick="showPage('users')">
                👥 用户管理
            </div>
            <div class="nav-item" onclick="showPage('groups')">
                🏷️ 用户组管理
            </div>
            <div class="nav-item" onclick="showPage('folders')">
                📁 共享文件夹
            </div>
            <div class="nav-item" onclick="showPage('settings')">
                ⚙️ 系统设置
            </div>
            <div class="nav-item" onclick="showPage('monitor')">
                📈 监控统计
            </div>
            <div class="nav-item" onclick="showPage('logs')">
                📋 活动日志
            </div>
        </div>

        <div class="main-content">
            <!-- 仪表板页面 -->
            <div id="dashboard" class="page active">
                <h2>系统概览</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3 id="totalUsers">-</h3>
                        <p>总用户数</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalFiles">-</h3>
                        <p>总文件数</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="onlineUsers">-</h3>
                        <p>在线用户</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="todayDownloads">-</h3>
                        <p>今日下载</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2>最近活动</h2>
                    </div>
                    <div class="card-body">
                        <div id="recentActivities" class="loading"></div>
                    </div>
                </div>
            </div>

            <!-- 用户管理页面 -->
            <div id="users" class="page">
                <div class="card">
                    <div class="card-header">
                        <h2>用户管理</h2>
                        <button class="btn btn-primary" onclick="showCreateUserModal()">添加用户</button>
                    </div>
                    <div class="card-body">
                        <div id="usersTable" class="loading"></div>
                    </div>
                </div>
            </div>

            <!-- 用户组管理页面 -->
            <div id="groups" class="page">
                <div class="card">
                    <div class="card-header">
                        <h2>用户组管理</h2>
                        <button class="btn btn-primary" onclick="showCreateGroupModal()">添加用户组</button>
                    </div>
                    <div class="card-body">
                        <div id="groupsTable" class="loading"></div>
                    </div>
                </div>
            </div>

            <!-- 共享文件夹页面 -->
            <div id="folders" class="page">
                <div class="card">
                    <div class="card-header">
                        <h2>共享文件夹管理</h2>
                        <button class="btn btn-primary" onclick="showCreateFolderModal()">添加共享文件夹</button>
                    </div>
                    <div class="card-body">
                        <div id="foldersTable" class="loading"></div>
                    </div>
                </div>
            </div>

            <!-- 系统设置页面 -->
            <div id="settings" class="page">
                <div class="card">
                    <div class="card-header">
                        <h2>系统设置</h2>
                    </div>
                    <div class="card-body">
                        <div id="settingsContent" class="loading"></div>
                    </div>
                </div>
            </div>

            <!-- 监控统计页面 -->
            <div id="monitor" class="page">
                <div class="card">
                    <div class="card-header">
                        <h2>监控统计</h2>
                    </div>
                    <div class="card-body">
                        <div id="monitorContent" class="loading"></div>
                    </div>
                </div>
            </div>

            <!-- 活动日志页面 -->
            <div id="logs" class="page">
                <div class="card">
                    <div class="card-header">
                        <h2>活动日志</h2>
                    </div>
                    <div class="card-body">
                        <div id="logsTable" class="loading"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="modalBody">
                <!-- 模态框内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script src="admin_dashboard.js"></script>
</body>
</html>
