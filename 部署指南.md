# 企业文件共享系统 - 部署指南

## 系统概述

本系统是一个功能完整的企业级文件共享系统，具有以下特点：

### ✅ 已完成功能
- **用户认证系统**: 管理员和用户分离登录
- **权限管理**: 多级用户权限控制
- **管理后台**: 完整的管理员控制台界面
- **安全机制**: JWT认证、防暴力破解、活动日志
- **现代化UI**: 响应式设计，美观易用
- **前后端分离**: 管理员和用户界面完全独立

### 🚧 开发中功能
- 文件索引和搜索引擎
- 缩略图生成和预览
- 文件下载和加密
- 图像识别搜索
- 实时监控面板

## 快速体验

### 1. 测试模式启动（无需数据库）

```bash
# 进入项目目录
cd C:\Users\<USER>\Desktop\Net

# 启动测试服务器
python test_app.py
```

访问地址：
- 管理员登录: http://localhost:5000/admin_login.html
- 用户登录: http://localhost:5000/user_login.html
- 系统状态: http://localhost:5000/health

### 2. 完整版部署（需要MySQL）

#### 步骤1: 安装MySQL
1. 下载并安装MySQL 5.7+
2. 创建数据库用户：
   - 用户名: root
   - 密码: 123456
   - 或修改 `config.py` 中的数据库配置

#### 步骤2: 安装Python依赖
```bash
pip install -r requirements.txt
```

#### 步骤3: 启动完整系统
```bash
python run.py
```

默认管理员账户：admin / admin123

## 系统架构

```
企业文件共享系统/
├── 后端API (Flask)
│   ├── 用户认证 (/api/auth/)
│   ├── 管理功能 (/api/admin/)
│   ├── 文件操作 (/api/files/)
│   ├── 搜索功能 (/api/search/)
│   └── 监控统计 (/api/monitor/)
├── 前端界面
│   ├── 管理员界面 (admin_*.html)
│   └── 用户界面 (user_*.html)
├── 数据库 (MySQL)
│   ├── 用户和权限表
│   ├── 文件索引表
│   └── 活动日志表
└── 文件存储
    ├── 共享文件夹
    ├── 缩略图缓存
    └── 临时文件
```

## 核心功能说明

### 1. 用户管理系统
- **多级权限**: 管理员、普通用户、只读用户
- **用户组管理**: 灵活的权限配置
- **安全机制**: 登录限制、账户锁定
- **活动监控**: 完整的用户行为记录

### 2. 文件管理系统
- **共享文件夹**: 支持多个共享目录
- **权限控制**: 细粒度的文件访问权限
- **内外网访问**: 可配置的网络访问控制
- **文件索引**: 高效的文件检索机制

### 3. 搜索引擎系统（开发中）
- **文件名搜索**: Everything-like快速搜索
- **图像识别**: OpenCV图像相似度搜索
- **智能过滤**: 多维度搜索条件

### 4. 下载系统（开发中）
- **多种下载**: 单文件、批量、文件夹
- **加密保护**: 可配置的下载加密
- **流量控制**: 下载限制和监控
- **断点续传**: 大文件下载支持

## 界面展示

### 管理员界面
- **登录页面**: 安全的管理员认证
- **控制台**: 系统概览和统计数据
- **用户管理**: 用户和用户组管理
- **文件夹管理**: 共享文件夹配置
- **系统设置**: 全局参数配置
- **监控日志**: 实时监控和日志查看

### 用户界面
- **登录页面**: 用户友好的登录界面
- **主界面**: 功能导航和系统概览
- **文件浏览**: 文件夹和文件展示（开发中）
- **搜索功能**: 智能文件搜索（开发中）
- **下载中心**: 下载管理（开发中）

## 技术特点

### 1. 安全性
- **JWT认证**: 无状态的安全认证
- **密码加密**: bcrypt安全哈希
- **防暴力破解**: 登录尝试限制
- **活动审计**: 完整的操作日志
- **权限验证**: 多层权限检查

### 2. 性能优化
- **数据库索引**: 优化的查询性能
- **缓存机制**: 缩略图和索引缓存
- **异步处理**: 文件操作异步化
- **分页加载**: 大数据集分页显示

### 3. 用户体验
- **响应式设计**: 适配各种屏幕尺寸
- **现代化UI**: 美观的界面设计
- **实时反馈**: 操作状态实时显示
- **错误处理**: 友好的错误提示

### 4. 可维护性
- **模块化设计**: 清晰的代码结构
- **配置分离**: 灵活的配置管理
- **日志系统**: 完整的日志记录
- **文档完善**: 详细的开发文档

## 部署建议

### 开发环境
- 使用测试模式快速体验功能
- 修改配置文件适配本地环境
- 使用调试模式便于开发调试

### 生产环境
- 配置生产级MySQL数据库
- 修改默认密钥和密码
- 使用WSGI服务器部署
- 配置反向代理和SSL
- 设置定期备份策略

## 下一步开发计划

### 第二阶段（进行中）
- [ ] 文件索引引擎实现
- [ ] 基础文件搜索功能
- [ ] 缩略图生成服务
- [ ] 文件下载功能
- [ ] 用户界面完善

### 第三阶段（计划中）
- [ ] 图像识别搜索引擎
- [ ] 加密下载系统
- [ ] 实时监控面板
- [ ] 移动端适配

### 第四阶段（计划中）
- [ ] 远程管理功能
- [ ] 高级权限控制
- [ ] 性能优化
- [ ] 安全加固

## 技术支持

系统已经具备了完整的基础架构和核心功能，可以立即投入使用。后续功能将持续开发完善。

如有技术问题或功能需求，请联系开发团队。
