<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件共享系统 - 用户界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .welcome-card h2 {
            color: #333;
            margin-bottom: 1rem;
        }

        .welcome-card p {
            color: #666;
            font-size: 16px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            color: #333;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }

        .btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
        }

        .stats-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .stat-item .number {
            font-size: 2rem;
            font-weight: bold;
            color: #74b9ff;
            margin-bottom: 0.5rem;
        }

        .stat-item .label {
            color: #666;
            font-size: 14px;
        }

        .notification-banner {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .scrolling-text {
            white-space: nowrap;
            overflow: hidden;
            animation: scroll 20s linear infinite;
        }

        @keyframes scroll {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }

        .footer {
            text-align: center;
            padding: 2rem;
            color: #666;
            font-size: 14px;
        }

        .coming-soon {
            background: #e3f2fd;
            color: #1976d2;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            margin-top: 1rem;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>企业文件共享系统</h1>
        <div class="user-info">
            <span id="userName">用户</span>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>
    </div>

    <div class="container">
        <div class="notification-banner">
            <div class="scrolling-text">
                📢 欢迎使用企业文件共享系统！请注意文件安全，合理使用系统资源。如有问题请联系管理员。
            </div>
        </div>

        <div class="welcome-card">
            <h2>欢迎回来！</h2>
            <p>您可以通过下方功能模块快速访问文件、搜索内容或查看统计信息</p>
        </div>

        <div class="stats-section">
            <h3 style="margin-bottom: 1.5rem; color: #333;">系统概览</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="number" id="totalFiles">-</div>
                    <div class="label">可访问文件</div>
                </div>
                <div class="stat-item">
                    <div class="number" id="todayDownloads">-</div>
                    <div class="label">今日下载</div>
                </div>
                <div class="stat-item">
                    <div class="number" id="onlineUsers">-</div>
                    <div class="label">在线用户</div>
                </div>
                <div class="stat-item">
                    <div class="number" id="myDownloads">-</div>
                    <div class="label">我的下载</div>
                </div>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="icon">📁</div>
                <h3>文件浏览</h3>
                <p>浏览和管理共享文件夹中的文件，支持多种文件格式预览</p>
                <button class="btn" onclick="showComingSoon()">浏览文件</button>
            </div>

            <div class="feature-card">
                <div class="icon">🔍</div>
                <h3>智能搜索</h3>
                <p>快速搜索文件名或使用图像识别功能查找相似图片</p>
                <button class="btn" onclick="showComingSoon()">开始搜索</button>
            </div>

            <div class="feature-card">
                <div class="icon">⬇️</div>
                <h3>文件下载</h3>
                <p>安全下载文件，支持单文件、批量下载和文件夹打包</p>
                <button class="btn" onclick="showComingSoon()">下载中心</button>
            </div>

            <div class="feature-card">
                <div class="icon">⬆️</div>
                <h3>文件上传</h3>
                <p>上传文件到指定文件夹，支持拖拽上传和批量上传</p>
                <button class="btn" onclick="showComingSoon()">上传文件</button>
            </div>

            <div class="feature-card">
                <div class="icon">🖼️</div>
                <h3>图片预览</h3>
                <p>支持多种图片格式的缩略图预览和大图查看</p>
                <button class="btn" onclick="showComingSoon()">图片库</button>
            </div>

            <div class="feature-card">
                <div class="icon">📊</div>
                <h3>使用统计</h3>
                <p>查看个人使用统计、下载记录和活动历史</p>
                <button class="btn" onclick="showComingSoon()">查看统计</button>
            </div>
        </div>

        <div class="coming-soon" id="comingSoonAlert" style="display: none;">
            🚧 该功能正在开发中，敬请期待！
        </div>
    </div>

    <div class="footer">
        <p>&copy; 2024 企业文件共享系统. 版权所有.</p>
    </div>

    <script>
        const API_BASE = '/api';
        let currentUser = null;

        // 页面加载时初始化
        window.addEventListener('load', function() {
            checkAuth();
            loadStats();
        });

        // 检查认证状态
        async function checkAuth() {
            const token = localStorage.getItem('access_token');
            if (!token) {
                window.location.href = 'user_login.html';
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.valid && !data.user.is_admin) {
                        currentUser = data.user;
                        document.getElementById('userName').textContent = data.user.username;
                    } else {
                        throw new Error('Not user');
                    }
                } else {
                    throw new Error('Invalid token');
                }
            } catch (error) {
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                localStorage.removeItem('user_info');
                window.location.href = 'user_login.html';
            }
        }

        // 加载统计数据
        async function loadStats() {
            // 模拟数据，实际应该从API获取
            document.getElementById('totalFiles').textContent = '1,234';
            document.getElementById('todayDownloads').textContent = '56';
            document.getElementById('onlineUsers').textContent = '12';
            document.getElementById('myDownloads').textContent = '8';
        }

        // 退出登录
        async function logout() {
            try {
                const token = localStorage.getItem('access_token');
                if (token) {
                    await fetch(`${API_BASE}/auth/logout`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                }
            } catch (error) {
                console.error('Logout failed:', error);
            } finally {
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                localStorage.removeItem('user_info');
                window.location.href = 'user_login.html';
            }
        }

        // 显示开发中提示
        function showComingSoon() {
            const alert = document.getElementById('comingSoonAlert');
            alert.style.display = 'block';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
