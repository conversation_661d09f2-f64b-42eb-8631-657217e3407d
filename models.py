from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timezone
from werkzeug.security import generate_password_hash, check_password_hash
import json

db = SQLAlchemy()

class User(db.Model):
    """用户表"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    is_admin = db.Column(db.<PERSON>, default=False, nullable=False)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    user_group_id = db.Column(db.Integer, db.ForeignKey('user_groups.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime, nullable=True)
    
    # 关系
    user_group = db.relationship('UserGroup', backref='users')
    download_records = db.relationship('DownloadRecord', backref='user', lazy='dynamic')
    activity_logs = db.relationship('ActivityLog', backref='user', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_admin': self.is_admin,
            'is_active': self.is_active,
            'user_group': self.user_group.name if self.user_group else None,
            'created_at': self.created_at.isoformat(),
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class UserGroup(db.Model):
    """用户组表"""
    __tablename__ = 'user_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    permissions = db.Column(db.Text, nullable=False)  # JSON格式存储权限
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def get_permissions(self):
        return json.loads(self.permissions) if self.permissions else {}
    
    def set_permissions(self, permissions_dict):
        self.permissions = json.dumps(permissions_dict)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'permissions': self.get_permissions(),
            'created_at': self.created_at.isoformat(),
            'user_count': len(self.users)
        }

class SharedFolder(db.Model):
    """共享文件夹表"""
    __tablename__ = 'shared_folders'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    path = db.Column(db.Text, nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    allow_internal = db.Column(db.Boolean, default=True)
    allow_external = db.Column(db.Boolean, default=False)
    permissions = db.Column(db.Text, nullable=False)  # JSON格式存储权限配置
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_scanned = db.Column(db.DateTime, nullable=True)
    file_count = db.Column(db.Integer, default=0)
    total_size = db.Column(db.BigInteger, default=0)
    
    def get_permissions(self):
        return json.loads(self.permissions) if self.permissions else {}
    
    def set_permissions(self, permissions_dict):
        self.permissions = json.dumps(permissions_dict)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'path': self.path,
            'description': self.description,
            'is_active': self.is_active,
            'allow_internal': self.allow_internal,
            'allow_external': self.allow_external,
            'permissions': self.get_permissions(),
            'created_at': self.created_at.isoformat(),
            'last_scanned': self.last_scanned.isoformat() if self.last_scanned else None,
            'file_count': self.file_count,
            'total_size': self.total_size
        }

class FileIndex(db.Model):
    """文件索引表"""
    __tablename__ = 'file_index'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False, index=True)
    filepath = db.Column(db.Text, nullable=False, index=True)
    file_size = db.Column(db.BigInteger, nullable=False)
    file_type = db.Column(db.String(50), nullable=False, index=True)
    file_extension = db.Column(db.String(20), nullable=False, index=True)
    shared_folder_id = db.Column(db.Integer, db.ForeignKey('shared_folders.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    modified_at = db.Column(db.DateTime, nullable=False)
    is_image = db.Column(db.Boolean, default=False, index=True)
    has_thumbnail = db.Column(db.Boolean, default=False)
    image_features = db.Column(db.Text, nullable=True)  # 图像特征向量，用于图像搜索

    # 关系
    shared_folder = db.relationship('SharedFolder', backref='files')

    def to_dict(self):
        return {
            'id': self.id,
            'filename': self.filename,
            'filepath': self.filepath,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'file_extension': self.file_extension,
            'shared_folder_id': self.shared_folder_id,
            'shared_folder_name': self.shared_folder.name if self.shared_folder else None,
            'created_at': self.created_at.isoformat(),
            'modified_at': self.modified_at.isoformat(),
            'is_image': self.is_image,
            'has_thumbnail': self.has_thumbnail
        }

class DownloadRecord(db.Model):
    """下载记录表"""
    __tablename__ = 'download_records'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    file_id = db.Column(db.Integer, db.ForeignKey('file_index.id'), nullable=True)
    download_type = db.Column(db.String(20), nullable=False)  # single, batch, folder
    file_count = db.Column(db.Integer, default=1)
    total_size = db.Column(db.BigInteger, nullable=False)
    is_encrypted = db.Column(db.Boolean, default=False)
    download_ip = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime, nullable=True)
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed

    # 关系
    file = db.relationship('FileIndex', backref='download_records')

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'file_id': self.file_id,
            'filename': self.file.filename if self.file else None,
            'download_type': self.download_type,
            'file_count': self.file_count,
            'total_size': self.total_size,
            'is_encrypted': self.is_encrypted,
            'download_ip': self.download_ip,
            'created_at': self.created_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'status': self.status
        }

class ActivityLog(db.Model):
    """活动日志表"""
    __tablename__ = 'activity_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    action = db.Column(db.String(50), nullable=False, index=True)
    resource_type = db.Column(db.String(50), nullable=False)
    resource_id = db.Column(db.Integer, nullable=True)
    details = db.Column(db.Text, nullable=True)
    ip_address = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    severity = db.Column(db.String(20), default='info')  # info, warning, error, critical

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else 'System',
            'action': self.action,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'created_at': self.created_at.isoformat(),
            'severity': self.severity
        }

class SystemSettings(db.Model):
    """系统设置表"""
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text, nullable=True)
    description = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), nullable=False, index=True)
    data_type = db.Column(db.String(20), default='string')  # string, int, float, bool, json
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_value(self):
        if self.data_type == 'int':
            return int(self.value) if self.value else 0
        elif self.data_type == 'float':
            return float(self.value) if self.value else 0.0
        elif self.data_type == 'bool':
            return self.value.lower() in ('true', '1', 'yes') if self.value else False
        elif self.data_type == 'json':
            return json.loads(self.value) if self.value else {}
        else:
            return self.value or ''

    def set_value(self, value):
        if self.data_type == 'json':
            self.value = json.dumps(value)
        else:
            self.value = str(value)

    def to_dict(self):
        return {
            'id': self.id,
            'key': self.key,
            'value': self.get_value(),
            'description': self.description,
            'category': self.category,
            'data_type': self.data_type,
            'updated_at': self.updated_at.isoformat()
        }
