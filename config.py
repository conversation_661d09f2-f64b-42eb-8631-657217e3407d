import os
from datetime import timedelta

class Config:
    # 数据库配置
    MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
    MYSQL_PORT = int(os.getenv('MYSQL_PORT', 3306))
    MYSQL_USER = os.getenv('MYSQL_USER', 'root')
    MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '123456')
    MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', 'file_sharing_system')
    
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'echo': False
    }
    
    # JWT配置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # 文件存储配置
    SHARED_FOLDERS = []  # 将在运行时从数据库加载
    THUMBNAIL_FOLDER = os.path.join(os.getcwd(), 'thumbnails')
    TEMP_FOLDER = os.path.join(os.getcwd(), 'temp')
    
    # 缩略图配置
    THUMBNAIL_SIZES = {
        'small': (150, 150),
        'medium': (300, 300),
        'large': (600, 600),
        'xlarge': (1200, 1200)
    }
    
    # 支持的图片格式
    SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.psd', '.ai', '.eps']
    
    # 下载配置
    MAX_DOWNLOAD_SIZE = 1024 * 1024 * 1024  # 1GB
    MAX_BATCH_DOWNLOAD_COUNT = 100
    ENCRYPTION_THRESHOLD = 5  # 下载5次后开始加密
    
    # 搜索引擎配置
    ENABLE_FILE_SEARCH = True
    ENABLE_IMAGE_SEARCH = True
    
    # 网络访问配置
    ENABLE_EXTERNAL_ACCESS = False
    ALLOWED_EXTERNAL_IPS = []
    
    # 系统配置
    MAX_CONCURRENT_USERS = 100
    RATE_LIMIT_PER_MINUTE = 60
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'file_sharing_system.log'
    
    # 安全配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')
    
    @staticmethod
    def init_app(app):
        # 创建必要的目录
        os.makedirs(Config.THUMBNAIL_FOLDER, exist_ok=True)
        os.makedirs(Config.TEMP_FOLDER, exist_ok=True)

class DevelopmentConfig(Config):
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    DEBUG = False
    LOG_LEVEL = 'WARNING'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
