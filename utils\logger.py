import logging
import os
from logging.handlers import RotatingFileHandler
from datetime import datetime

def setup_logger(app):
    """设置应用日志"""
    if not app.debug:
        # 创建日志目录
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        # 设置文件日志处理器
        file_handler = RotatingFileHandler(
            'logs/file_sharing_system.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('File Sharing System startup')

def log_activity(user_id, action, resource_type, resource_id=None, details=None, ip_address=None, severity='info'):
    """记录用户活动"""
    from models import db, ActivityLog
    
    try:
        log = ActivityLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details,
            ip_address=ip_address or '127.0.0.1',
            severity=severity
        )
        db.session.add(log)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logging.error(f"Failed to log activity: {e}")

def log_download(user_id, file_id, download_type, file_count, total_size, is_encrypted, ip_address, user_agent=None):
    """记录下载活动"""
    from models import db, DownloadRecord
    
    try:
        record = DownloadRecord(
            user_id=user_id,
            file_id=file_id,
            download_type=download_type,
            file_count=file_count,
            total_size=total_size,
            is_encrypted=is_encrypted,
            download_ip=ip_address,
            user_agent=user_agent
        )
        db.session.add(record)
        db.session.commit()
        return record.id
    except Exception as e:
        db.session.rollback()
        logging.error(f"Failed to log download: {e}")
        return None
