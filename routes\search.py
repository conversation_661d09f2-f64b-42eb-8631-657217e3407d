from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

search_bp = Blueprint('search', __name__)

@search_bp.route('/files', methods=['GET'])
@jwt_required()
def search_files():
    """文件搜索"""
    return jsonify({'message': '文件搜索功能待实现'}), 200

@search_bp.route('/images', methods=['POST'])
@jwt_required()
def search_images():
    """图像搜索"""
    return jsonify({'message': '图像搜索功能待实现'}), 200
