from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

download_bp = Blueprint('download', __name__)

@download_bp.route('/file/<int:file_id>', methods=['GET'])
@jwt_required()
def download_file(file_id):
    """下载单个文件"""
    return jsonify({'message': '文件下载功能待实现'}), 200

@download_bp.route('/batch', methods=['POST'])
@jwt_required()
def download_batch():
    """批量下载"""
    return jsonify({'message': '批量下载功能待实现'}), 200
